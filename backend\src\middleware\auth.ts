import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { verifyAccessToken, extractTokenFromHeader } from '@/utils/jwt';
import { AuthenticationError, AuthorizationError } from '@/utils/errors';
import { AuthRequest, AuthenticatedUser } from '@/types';

const prisma = new PrismaClient();

/**
 * Authentication middleware to verify JWT tokens
 * Adds user information to request object if token is valid
 */
export const authenticate = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      throw new AuthenticationError('Access token required');
    }

    // Verify the token
    const payload = verifyAccessToken(token);
    
    // Get user from database to ensure they still exist and are active
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        isActive: true,
        lockedUntil: true,
      },
    });

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    if (!user.isActive) {
      throw new AuthenticationError('Account is deactivated');
    }

    if (user.lockedUntil && user.lockedUntil > new Date()) {
      throw new AuthenticationError('Account is temporarily locked');
    }

    // Add user to request object
    req.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role as 'ADMIN' | 'CLINICIAN' | 'STAFF',
      firstName: user.firstName,
      lastName: user.lastName,
    };

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Authorization middleware to check user roles
 * Must be used after authenticate middleware
 */
export const authorize = (allowedRoles: ('ADMIN' | 'CLINICIAN' | 'STAFF')[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new AuthenticationError('Authentication required');
      }

      if (!allowedRoles.includes(req.user.role)) {
        throw new AuthorizationError(
          `Access denied. Required roles: ${allowedRoles.join(', ')}`
        );
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Optional authentication middleware
 * Adds user information if token is present and valid, but doesn't require it
 */
export const optionalAuth = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (token) {
      try {
        const payload = verifyAccessToken(token);
        
        const user = await prisma.user.findUnique({
          where: { id: payload.userId },
          select: {
            id: true,
            username: true,
            email: true,
            role: true,
            firstName: true,
            lastName: true,
            isActive: true,
            lockedUntil: true,
          },
        });

        if (user && user.isActive && (!user.lockedUntil || user.lockedUntil <= new Date())) {
          req.user = {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role as 'ADMIN' | 'CLINICIAN' | 'STAFF',
            firstName: user.firstName,
            lastName: user.lastName,
          };
        }
      } catch {
        // Ignore token errors for optional auth
      }
    }

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to check if user can access specific patient data
 * Clinicians can only access their own patients, admins can access all
 */
export const authorizePatientAccess = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      throw new AuthenticationError('Authentication required');
    }

    // Admins can access all patients
    if (req.user!.role === 'ADMIN') {
      return next();
    }

    const patientId = req.params.id || req.params.patientId || req.body.patientId;
    
    if (!patientId) {
      throw new AuthorizationError('Patient ID required');
    }

    // Check if the patient was created by this user or if user is admin
    const patient = await prisma.patient.findUnique({
      where: { id: patientId },
      select: { createdBy: true },
    });

    if (!patient) {
      throw new AuthorizationError('Patient not found');
    }

    if (patient.createdBy !== req.user!.id && !['ADMIN'].includes(req.user!.role)) {
      throw new AuthorizationError('Access denied to this patient record');
    }

    next();
  } catch (error) {
    next(error);
  }
};
