// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(uuid())
  username      String    @unique
  email         String    @unique
  password      String    // bcrypt hash
  role          String    @default("CLINICIAN") // ADMIN, CLINICIAN, STAFF
  firstName     String
  lastName      String
  phone         String?   // Added for notification service
  isActive      Boolean   @default(true)
  lastLogin     DateTime?
  loginAttempts Int       @default(0)
  lockedUntil   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  createdPatients Patient[] @relation("PatientCreatedBy")
  providedAppointments Appointment[] @relation("AppointmentsProvidedBy")
  createdAppointments  Appointment[] @relation("AppointmentsCreatedBy")
  refreshTokens   RefreshToken[]
  auditLogs       AuditLog[]
  notifications   Notification[] @relation("NotificationRecipient")
  assessments     PatientAssessment[]
  createdLabResults LabResult[] @relation("LabResultCreator")
  providedRecurringAppointments RecurringAppointment[] @relation("RecurringAppointmentProvider")

  @@index([email])
  @@index([username])
  @@index([role])
  @@index([isActive])
  @@index([lastLogin])
  @@map("users")
}

model RefreshToken {
  id        String   @id @default(uuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  isRevoked Boolean  @default(false)

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([expiresAt])
  @@index([isRevoked])
  @@map("refresh_tokens")
}

model Patient {
  id               String          @id @default(uuid())
  patientId        String          @unique // Human-readable ID (e.g., P-2024-001)
  firstName        String
  lastName         String
  dateOfBirth      DateTime
  gender           String          // MALE, FEMALE, NON_BINARY, PREFER_NOT_TO_SAY, OTHER
  phone            String?
  email            String?
  address          String?         // JSON string for structured address object
  occupation       String?
  education        String?         // ELEMENTARY, HIGH_SCHOOL, SOME_COLLEGE, BACHELORS, MASTERS, DOCTORATE, PROFESSIONAL, OTHER
  maritalStatus    String?         // SINGLE, MARRIED, DIVORCED, WIDOWED, SEPARATED, DOMESTIC_PARTNERSHIP, OTHER
  emergencyContact String?         // JSON string: {name, phone, relationship}
  insuranceInfo    String?         // JSON string: {provider, policyNumber, groupNumber}
  medicalHistory   String?
  allergies        String?
  currentMeds      String?
  notes            String?
  isActive         Boolean         @default(true)
  isDeleted        Boolean         @default(false)
  deletedAt        DateTime?
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  createdBy        String

  // Relations
  creator              User                   @relation("PatientCreatedBy", fields: [createdBy], references: [id])
  labResults           LabResult[]
  appointments         Appointment[]
  auditLogs            AuditLog[]
  notifications        Notification[]
  recurringAppointments RecurringAppointment[]
  assessments          PatientAssessment[]

  @@index([lastName, firstName])
  @@index([dateOfBirth])
  @@index([gender])
  @@index([isActive])
  @@index([isDeleted])
  @@index([createdAt])
  @@index([createdBy])
  @@map("patients")
}

model LabResult {
  id           String       @id @default(uuid())
  patientId    String
  testType     String       // CBC, METABOLIC_PANEL, LIPID_PANEL, THYROID, etc.
  testDate     DateTime
  orderedBy    String       // Doctor name
  labName      String?      // Lab facility name
  results      String       // JSON string for structured lab values
  normalRanges String?      // JSON string for reference ranges
  flags        String?      // JSON string for abnormal indicators and alerts
  notes        String?
  status       String       @default("COMPLETED") // PENDING, IN_PROGRESS, COMPLETED, CANCELLED, AMENDED
  createdBy    String       // Added for lab result creator tracking
  isDeleted    Boolean      @default(false)
  deletedAt    DateTime?
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  // Relations
  patient       Patient        @relation(fields: [patientId], references: [id], onDelete: Cascade)
  creator       User           @relation("LabResultCreator", fields: [createdBy], references: [id])
  auditLogs     AuditLog[]
  notifications Notification[]

  @@index([patientId])
  @@index([testType])
  @@index([testDate])
  @@index([status])
  @@index([createdBy])
  @@index([isDeleted])
  @@index([createdAt])
  @@index([patientId, testDate])
  @@map("lab_results")
}

model Appointment {
  id        String            @id @default(uuid())
  patientId String
  providerId String?
  recurringAppointmentId String?
  date      DateTime
  endTime   DateTime?
  duration  Int               // Duration in minutes
  type      String            // INITIAL_CONSULTATION, FOLLOW_UP, THERAPY_SESSION, etc.
  status    String            @default("SCHEDULED") // SCHEDULED, CONFIRMED, IN_PROGRESS, COMPLETED, etc.
  title          String?
  description    String?
  location       String?
  isVirtual      Boolean           @default(false)
  virtualMeetingUrl String?
  notes     String?
  isDeleted Boolean           @default(false)
  deletedAt DateTime?
  createdBy      String?
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt

  // Relations
  patient       Patient        @relation(fields: [patientId], references: [id], onDelete: Cascade)
  provider      User?          @relation("AppointmentsProvidedBy", fields: [providerId], references: [id])
  creator       User?          @relation("AppointmentsCreatedBy", fields: [createdBy], references: [id])
  auditLogs     AuditLog[]
  notifications Notification[]
  recurringAppointment RecurringAppointment? @relation(fields: [recurringAppointmentId], references: [id])

  @@index([patientId])
  @@index([providerId])
  @@index([date])
  @@index([status])
  @@index([type])
  @@index([isDeleted])
  @@index([patientId, date])
  @@map("appointments")
}

model Notification {
  id            String    @id @default(uuid())
  recipientId   String
  type          String    // APPOINTMENT_REMINDER, LAB_RESULT, MEDICATION_REMINDER, SYSTEM_ALERT, GENERAL
  title         String
  message       String
  priority      String    @default("MEDIUM") // LOW, MEDIUM, HIGH, URGENT
  channel       String    @default("IN_APP") // Added for notification channel tracking
  status        String    @default("PENDING") // Added for notification status tracking
  isRead        Boolean   @default(false)
  isProcessed   Boolean   @default(false)
  scheduledFor  DateTime?
  processedAt   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  patientId     String?
  appointmentId String?
  labResultId   String?

  // Relations
  recipient   User         @relation("NotificationRecipient", fields: [recipientId], references: [id])
  patient     Patient?     @relation(fields: [patientId], references: [id])
  appointment Appointment? @relation(fields: [appointmentId], references: [id])
  labResult   LabResult?   @relation(fields: [labResultId], references: [id])

  @@index([recipientId])
  @@index([type])
  @@index([channel])
  @@index([status])
  @@index([isRead])
  @@index([isProcessed])
  @@index([scheduledFor])
  @@index([createdAt])
  @@index([recipientId, isRead])
  @@map("notifications")
}

model RecurringAppointment {
  id             String    @id @default(uuid())
  patientId      String
  providerId     String    // Added for recurring appointment provider tracking
  startDate      DateTime
  endDate        DateTime?
  duration       Int       // Duration in minutes
  type           String    // INITIAL_CONSULTATION, FOLLOW_UP, THERAPY_SESSION, etc.
  frequency      String    // DAILY, WEEKLY, BIWEEKLY, MONTHLY, QUARTERLY
  interval       Int       @default(1) // Added for recurring appointment interval
  dayOfWeek      Int?      // 0-6, Sunday = 0
  dayOfMonth     Int?      // 1-31
  timeSlot       String    // HH:MM format
  notes          String?
  maxOccurrences Int?
  isActive       Boolean   @default(true)
  isDeleted      Boolean   @default(false)
  deletedAt      DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // Relations
  patient Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
  provider User @relation("RecurringAppointmentProvider", fields: [providerId], references: [id])
  appointments Appointment[]

  @@index([patientId])
  @@index([providerId])
  @@index([startDate])
  @@index([isActive])
  @@index([isDeleted])
  @@index([frequency])
  @@map("recurring_appointments")
}

model AuditLog {
  id           String    @id @default(uuid())
  userId       String
  action       String    // CREATE, UPDATE, DELETE, VIEW, EXPORT, CANCEL
  entityType   String    // USER, PATIENT, LAB_RESULT, APPOINTMENT, NOTIFICATION, RECURRING_APPOINTMENT
  entityId     String
  oldValues    String?   // JSON string for previous values (for updates)
  newValues    String?   // JSON string for new values (for creates/updates)
  ipAddress    String?
  userAgent    String?
  timestamp    DateTime  @default(now())
  patientId    String?   // For patient-related actions
  labResultId  String?   // For lab result actions
  appointmentId String?  // For appointment actions

  // Relations
  user        User         @relation(fields: [userId], references: [id])
  patient     Patient?     @relation(fields: [patientId], references: [id])
  labResult   LabResult?   @relation(fields: [labResultId], references: [id])
  appointment Appointment? @relation(fields: [appointmentId], references: [id])

  @@index([userId])
  @@index([action])
  @@index([entityType])
  @@index([entityId])
  @@index([timestamp])
  @@index([patientId])
  @@index([userId, timestamp])
  @@map("audit_logs")
}

model PatientAssessment {
  id            String    @id @default(uuid())
  patientId     String
  assessorId    String
  sessionDate   DateTime
  assessmentData String    // JSON string containing the full assessment data
  status        String    // in-progress, completed, reviewed, signed
  duration      Int       // in minutes
  isDeleted     Boolean   @default(false)
  deletedAt     DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  patient       Patient   @relation(fields: [patientId], references: [id], onDelete: Cascade)
  assessor      User      @relation(fields: [assessorId], references: [id])

  @@index([patientId])
  @@index([assessorId])
  @@index([sessionDate])
  @@index([status])
  @@index([isDeleted])
  @@index([createdAt])
  @@map("patient_assessments")
}

// Note: SQLite doesn't support enums, so we use String fields with comments indicating valid values
// Valid values are documented in the field comments above
