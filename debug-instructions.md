# AI Agent Debugging Protocol for Psychiatry App

## Mission
Systematically debug and fix the entire psychiatry patient management codebase using automated testing, structured logging, and incremental validation.

## Phase 1: Setup Debugging Infrastructure (Priority)

### 1.1 Install Testing & Logging Dependencies
```bash
# Frontend testing
npm install --save-dev vitest @testing-library/react @testing-library/jest-dom @testing-library/user-event jsdom

# Backend testing  
npm install --save-dev vitest supertest @types/supertest

# Logging
npm install winston pino pino-pretty
```

### 1.2 Create Test Configuration Files
Create `vitest.config.ts` in both frontend and backend:

**Frontend vitest.config.ts:**
```typescript
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
  },
})
```

**Backend vitest.config.ts:**
```typescript
import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    environment: 'node',
    setupFiles: ['./src/test/setup.ts'],
  },
})
```

### 1.3 Add Structured Logging
**Backend logging setup (`backend/src/utils/logger.ts`):**
```typescript
import winston from 'winston'

export const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
})
```

## Phase 2: Test-Driven Debugging Strategy

### 2.1 Create Health Check Tests First
Always start with basic connectivity tests:

**Backend API Health Test:**
```typescript
// backend/src/test/health.test.ts
import { describe, it, expect } from 'vitest'
import request from 'supertest'
import app from '../app'

describe('API Health Checks', () => {
  it('should respond to health check', async () => {
    const response = await request(app).get('/health')
    expect(response.status).toBe(200)
  })
  
  it('should connect to database', async () => {
    // Add database connection test
  })
})
```

**Frontend Component Smoke Test:**
```typescript
// frontend/src/test/App.test.tsx
import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import App from '../App'

describe('App Component', () => {
  it('renders without crashing', () => {
    render(<App />)
    expect(screen.getByRole('main')).toBeInTheDocument()
  })
})
```

### 2.2 Test Each Layer Independently

**Database Layer Testing:**
```typescript
// Test Prisma queries directly
describe('Database Operations', () => {
  it('should create patient', async () => {
    const patient = await prisma.patient.create({
      data: { name: 'Test Patient', email: '<EMAIL>' }
    })
    expect(patient.id).toBeDefined()
  })
})
```

**API Route Testing:**
```typescript
// Test each endpoint individually
describe('Patient Routes', () => {
  it('GET /api/patients returns patient list', async () => {
    const response = await request(app).get('/api/patients')
    expect(response.status).toBe(200)
    expect(Array.isArray(response.body)).toBe(true)
  })
})
```

**Frontend Integration Testing:**
```typescript
// Test user workflows
describe('Patient Management Flow', () => {
  it('should add new patient', async () => {
    render(<PatientForm />)
    // Simulate user interactions
    fireEvent.change(screen.getByLabelText(/name/i), { 
      target: { value: 'John Doe' } 
    })
    fireEvent.click(screen.getByRole('button', { name: /save/i }))
    // Assert expected behavior
  })
})
```

## Phase 3: Error Detection & Logging Protocol

### 3.1 Add Debug Logging to Critical Paths
Insert logging at these points:
- API request/response boundaries
- Database query entry/exit
- Authentication checks
- Error handling blocks
- State changes in React components

**Example logging pattern:**
```typescript
// In API routes
app.post('/api/patients', async (req, res) => {
  logger.info('Creating patient', { body: req.body })
  try {
    const patient = await patientService.create(req.body)
    logger.info('Patient created successfully', { patientId: patient.id })
    res.json(patient)
  } catch (error) {
    logger.error('Failed to create patient', { error: error.message, stack: error.stack })
    res.status(500).json({ error: 'Failed to create patient' })
  }
})
```

### 3.2 Component Error Boundaries
```typescript
// Add to React components
const ErrorBoundary: React.FC = ({ children }) => {
  return (
    <ErrorBoundary onError={(error) => {
      console.error('Component error:', error)
      // Log to external service if needed
    }}>
      {children}
    </ErrorBoundary>
  )
}
```

## Phase 4: Automated Debugging Execution

### 4.1 Run Tests in Sequence
Execute this debugging sequence:

```bash
# 1. Type checking
npm run type-check

# 2. Backend tests
cd backend && npm run test

# 3. Frontend tests  
cd frontend && npm run test

# 4. Integration tests
npm run test:integration

# 5. Build tests
npm run build
```

### 4.2 Analyze Test Output
For each failing test:
1. Read the error message completely
2. Check the logs for that specific operation
3. Identify the exact line causing the issue
4. Fix the smallest possible change
5. Re-run only that test
6. Move to next failing test

## Phase 5: AI Agent Instructions

### For Each Bug/Issue:
1. **Start Small**: Always test the smallest unit first (single function, single component)
2. **Use Logs**: Add console.log/logger statements before fixing
3. **Test Immediately**: Run the specific test after each change
4. **Document Fixes**: Comment what was fixed and why
5. **Incremental**: Never fix multiple issues at once

### When Stuck:
1. Create a minimal reproduction case
2. Add extensive logging around the problem area
3. Test with hardcoded values first
4. Check network tab in browser dev tools
5. Verify database state with Prisma Studio

### Success Criteria:
- All tests pass
- No console errors in browser
- All API endpoints return expected responses
- Database operations complete successfully
- Frontend components render without errors

## Commands to Execute:

```bash
# Install dependencies
npm install

# Setup tests
npm run test:setup  # You'll need to create this script

# Run full debugging suite
npm run debug:full  # You'll need to create this script

# Monitor logs during development
npm run dev:debug  # Start with enhanced logging
```

## Expected Output:
- Detailed test results showing exactly what passes/fails
- Structured logs showing request/response flow
- Clear error messages with file and line numbers
- Incremental fixes with verification at each step

Focus on getting the testing infrastructure working first, then systematically test each component. This approach gives you concrete, actionable feedback that AI agents can work with effectively.